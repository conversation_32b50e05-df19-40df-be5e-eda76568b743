// research/ReportWriterFormatterAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { SynthesizedFindings, FormattingGuidelines, ReportDraft } from './ResearchInterfaces';

export class ReportWriterFormatterAgent extends ResearchAgent {
    constructor(
        id: string = 'report-writer',
        name: string = 'Report Writer & Formatter',
        userId: string = '',
        defaultLlmProvider: LlmProvider = 'openai',
        defaultLlmModel: string = 'o3-2025-04-16'
    ) {
        const role = 'Report Writing and Formatting Specialist';
        const description = `I take synthesized research findings and craft well-structured, clearly written reports, summaries, or presentations according to specified formatting guidelines.`;
        super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
    }

    /**
     * Draft a report based on synthesized findings and guidelines
     * @param findings - The synthesized information
     * @param guidelines - Formatting and structure requirements
     * @param parentTaskId - The overall research task ID
     * @returns The drafted report
     */
    async draftReport(
        findings: SynthesizedFindings,
        guidelines: FormattingGuidelines,
        parentTaskId: string
    ): Promise<ReportDraft> {
        console.log(`[${this.name}] Drafting ${guidelines.format} for task ${parentTaskId}`);

        const reportTitle = `Research ${guidelines.format}: Findings on Sub-Task ${findings.subTaskId}`; // Example title

        // Prepare context for LLM generation
        const findingsContext = `
          Synthesized Summary: ${findings.summary}
          Key Themes: ${findings.keyThemes.join(', ')}
          Confidence Level: ${findings.confidenceLevel}
          Key Analyzed Sources Count: ${findings.analyzedSources.length}
          ${findings.contradictions?.length ? `Noted Contradictions: ${findings.contradictions.join('; ')}` : ''}
          ${findings.gaps?.length ? `Noted Gaps: ${findings.gaps.join('; ')}` : ''}
        `;
        // Maybe include top findings from analyzedSources if needed

        const formattingInstructions = `
          Required Format: ${guidelines.format}
          ${guidelines.styleGuide ? `Citation Style: ${guidelines.styleGuide}` : ''}
          ${guidelines.length ? `Approximate Length: ${guidelines.length}` : ''}
          ${guidelines.sections ? `Required Sections: ${guidelines.sections.join(', ')}` : ''}
        `;

        const prompt = `
          Based on the following synthesized research findings and formatting guidelines, write the research output.
          Ensure the language is clear, objective, and professional.
          Structure the output logically according to the required format and sections.
          If drafting a report, include an introduction, body sections based on themes, and a conclusion.
          Reference the synthesized findings accurately.
          If a citation style is specified, make placeholders or attempt basic formatting for a bibliography section based on the source URLs available in the analysis (list URLs).

          Synthesized Findings:
          ${findingsContext}

          Formatting Guidelines:
          ${formattingInstructions}

          Generate the full content for the ${guidelines.format}. Output only the content itself.
        `;

        const reportContent = await this.processRequest(prompt);

        // TODO: Implement more robust citation/bibliography generation based on findings.analyzedSources
        const bibliography = findings.analyzedSources.map(s => s.sourceUrl);

        const draft: ReportDraft = {
            draftId: `draft-${findings.synthesisId}-${Date.now()}`,
            synthesisId: findings.synthesisId,
            parentTaskId,
            title: reportTitle,
            content: reportContent, // Assume LLM returns markdown or plain text
            bibliography: bibliography, // Basic list for now
            version: 1,
        };

        console.log(`[${this.name}] Draft ${draft.draftId} created.`);
        return draft;
    }

    /**
     * Handles incoming tasks/messages for report writing
     */
    async handleTask(messageContent: string, metadata: Record<string, any>): Promise<void> {
        if (metadata.subTaskId && metadata.instruction && metadata.synthesis && metadata.formattingGuidelines) {
            console.log(`[${this.name}] Received task: ${metadata.subTaskId} - ${metadata.instruction}`);
            const findings: SynthesizedFindings = metadata.synthesis;
            const guidelines: FormattingGuidelines = metadata.formattingGuidelines;
            const parentTaskId = metadata.parentTaskId || 'unknown_parent';

            const draft = await this.draftReport(findings, guidelines, parentTaskId);

            // Send draft back to Lead or directly to QA?
            const leadAgentId = 'research-lead'; // Placeholder
            await this.sendMessage(
                leadAgentId,
                `Draft ready for review for sub-task ${metadata.subTaskId}`,
                {
                    type: 'draft_ready',
                    subTaskId: metadata.subTaskId,
                    draft: draft // Attach draft
                }
            );
             await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');

        } else {
            console.warn(`[${this.name}] Received message missing required data for writing:`, metadata);
        }
    }
}