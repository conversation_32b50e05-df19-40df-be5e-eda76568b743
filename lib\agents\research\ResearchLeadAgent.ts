// research/ResearchLeadAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { ResearchTaskRequest, ResearchTaskBrief, ResearchPlan, SubTask, FinalResearchReport, ReviewFeedback } from './ResearchInterfaces'; // Adjust path
import { PdfContent, PdfGenerationOptions } from '../../tools/pdf-generator';
import { Buffer } from 'node:buffer';

// PMO Integration Imports
import { AgenticTeamId, PMOFormInput, PMOContextOptions } from '../pmo/PMOInterfaces';
import { QueryDocumentsAgent } from '../../../components/Agents/QueryDocumentsAgent';
import { QuestionAnswerAgent } from '../../../components/Agents/QuestionAnswerAgent';
import { chartTool, ChartGenerationResult, CHART_TYPES } from '../../tools/chart-tool';
import { calendarTool } from '../../tools/calendarTool';
import { internetSearchTool } from '../../tools/internet-search';
import { adminDb } from '../../../components/firebase-admin';

// PMO Strategic Task Interfaces
interface StrategicTask {
  id: string;
  title: string;
  description: string;
  category: 'Market Intelligence' | 'Product Analysis' | 'Customer Intelligence' | 'Marketing Infrastructure' | 'Performance Metrics' | 'Customer Validation' | 'Strategic Planning' | 'Implementation' | 'Research' | 'Content Creation';
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  assignedTeam: 'Research Team' | 'Business Analysis Team' | 'Marketing Team' | 'Sales Team' | 'Software Design Team' | 'Content Team' | 'Strategic Director';
  status: 'IDENTIFIED' | 'ASSIGNED' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'BLOCKED' | 'CANCELLED';
  specificRequirements: string[];
  deliverable: string;
  timeline: {
    estimatedDuration: string;
    startDate?: Date;
    dueDate?: Date;
    milestones?: Array<{
      name: string;
      date: Date;
      description: string;
      completed: boolean;
    }>;
  };
  dependencies?: string[];
  blockers?: string[];
  successCriteria: string[];
  resources?: {
    budget?: number;
    tools?: string[];
    personnel?: string[];
    documents?: string[];
  };
  metadata: {
    createdAt: Date;
    createdBy: string;
    updatedAt: Date;
    source: 'Information Gap Analysis' | 'Strategic Planning' | 'PMO Requirements' | 'User Request' | 'System Generated';
    pmoId?: string;
    projectId?: string;
    requestId?: string;
  };
}

interface StrategicTaskCollection {
  collectionId: string;
  name: string;
  description: string;
  source: 'PMO Project' | 'Strategic Analysis' | 'Information Gap Resolution' | 'User Request' | 'Research Project';
  totalTasks: number;
  tasks: StrategicTask[];
  overallTimeline: {
    startDate: Date;
    estimatedEndDate: Date;
    criticalPath: string[];
  };
  teamAssignments: {
    [teamName: string]: {
      taskCount: number;
      taskIds: string[];
      estimatedWorkload: string;
    };
  };
  successMetrics: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface TaskGenerationContext {
  userRequest?: string;
  analysisContent?: string;
  strategicObjectives?: string[];
  constraints?: string[];
  pmoContext?: {
    pmoId: string;
    projectTitle: string;
    projectDescription: string;
    priority: string;
    category: string;
  };
  identifiedGaps?: string[];
  researchScope?: string;
  researchQuestions?: string[];
}

// PMO Integration Interface
interface IStrategicDirectorAgent {
  analyzeTask(params: {
    title: string;
    description: string;
    context: string;
    taskType: string;
  }): Promise<{
    success: boolean;
    recommendedTeams?: AgenticTeamId[];
    rationale?: string;
    error?: string;
  }>;
}

export class ResearchLeadAgent extends ResearchAgent implements IStrategicDirectorAgent {
  private activePlans: Map<string, ResearchPlan> = new Map(); // Store active plans in memory
  private activeStrategicCollections: Map<string, StrategicTaskCollection> = new Map(); // Store strategic task collections

  // PMO Integration Properties
  private queryDocumentsAgent: QueryDocumentsAgent;
  private questionAnswerAgent: QuestionAnswerAgent;
  private lastGapAnalysis: any = null; // Store information gap analysis results

  // Delegation Infrastructure
  private agentRoleMapping: Map<string, string> = new Map([
    ['info-retriever', 'info-retriever-1'],
    ['data-analyst', 'data-analyst'],
    ['report-writer', 'report-writer'],
    ['qa-reviewer', 'qa-reviewer'],
    ['strategic-planner', 'strategic-planner'],
    // Add mappings for task decomposition role names
    ['InformationRetrieval', 'info-retriever-1'],
    ['DataAnalystSynthesizer', 'data-analyst'],
    ['ReportWriterFormatter', 'report-writer'],
    ['QualityAssuranceReviewer', 'qa-reviewer']
  ]);

  constructor(
    id: string = 'research-lead',
    name: string = 'Research Lead',
    userId: string = '',
    defaultLlmProvider: LlmProvider = 'openai',
    defaultLlmModel: string = 'gpt-4o'
  ) {
    const role = 'Research Coordinator & Strategic Director';
    const description = `I manage comprehensive research processes and strategic planning. I receive research requests, clarify scope, decompose tasks, assign them to specialist agents across all teams, monitor progress, and deliver final reports. I also integrate with PMO workflows, analyze tasks for cross-team coordination, and create strategic implementation plans.`;
    super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);

    // Initialize PMO integration tools
    this.queryDocumentsAgent = new QueryDocumentsAgent({
      maxResults: 2,
      defaultTemperature: 0.3,
      defaultMaxTokens: 3000
    });

    this.questionAnswerAgent = new QuestionAnswerAgent();
  }

  /**
   * Initiate a new research task
   * @param request - The initial research request
   * @returns The research plan ID
   */
  async initiateResearchTask(request: ResearchTaskRequest): Promise<string> {
    console.log(`[${this.name}] Initiating research task: ${request.topic}`);
    const brief = await this.clarifyAndDefineTask(request);
    const plan = await this.decomposeTask(brief);
    this.activePlans.set(plan.planId, plan);

    // Store plan in agent memory
    this.memory.Agent_Response[`plan_${plan.planId}`] = plan;
    await this.saveMemoryToStorage();

    await this.assignAndMonitorTasks(plan);
    return plan.planId;
  }

  /**
   * Clarify the scope and define the brief using LLM
   */
  async clarifyAndDefineTask(request: ResearchTaskRequest): Promise<ResearchTaskBrief> {
    console.log(`[${this.name}] Clarifying task: ${request.taskId}`);
    const prompt = `
      You are a Research Lead Agent tasked with clarifying research requirements.

      Received research request:
      Topic: ${request.topic}
      Scope: ${request.scope || 'Not specified'}
      Depth: ${request.requiredDepth || 'moderate'}
      Format: ${request.outputFormat || 'report'}
      Deadline: ${request.deadline?.toISOString() || 'Not specified'}

      Based on this, please:
      1.  Refine the scope into a clear, actionable statement.
      2.  Identify 3-5 key research questions to answer.
      3.  Suggest potential source types or specific starting URLs/databases if applicable.
      4.  List any obvious constraints (e.g., date ranges implied by topic).

      IMPORTANT: You must respond with ONLY a valid JSON object. Do not include any explanatory text before or after the JSON.

      Required JSON format:
      {
        "clarifiedScope": "string",
        "keyQuestions": ["string1", "string2", "string3"],
        "potentialSources": ["string1", "string2"],
        "constraints": ["string1", "string2"]
      }
    `;
    const responseJson = await this.processRequest(prompt);
    try {
      const parsed = JSON.parse(responseJson);
      const brief: ResearchTaskBrief = {
        ...request,
        clarifiedScope: parsed.clarifiedScope || request.scope || `Research on ${request.topic}`,
        keyQuestions: parsed.keyQuestions || [`What are the key aspects of ${request.topic}?`],
        potentialSources: parsed.potentialSources || [],
        constraints: parsed.constraints || []
      };
      console.log(`[${this.name}] Task clarified: ${brief.clarifiedScope}`);
      return brief;
    } catch (error) {
      console.error(`[${this.name}] Failed to parse clarification response:`, error);
      // Return a basic brief on error with all required arrays
      return {
        ...request,
        clarifiedScope: `Research on ${request.topic}`,
        keyQuestions: [`Explore ${request.topic}`],
        potentialSources: [],
        constraints: []
      };
    }
  }

  /**
   * Decompose the task into sub-tasks for specialist agents
   */
  async decomposeTask(brief: ResearchTaskBrief): Promise<ResearchPlan> {
      console.log(`[${this.name}] Decomposing task: ${brief.taskId}`);
      const planId = `plan-${brief.taskId}-${Date.now()}`;
      const prompt = `
      You are a Research Lead Agent tasked with decomposing research tasks into sub-tasks.

      Given the research brief:
      Task ID: ${brief.taskId}
      Clarified Scope: ${brief.clarifiedScope}
      Key Questions: ${Array.isArray(brief.keyQuestions) ? brief.keyQuestions.join(', ') : 'General research questions'}
      Potential Sources: ${Array.isArray(brief.potentialSources) ? brief.potentialSources.join(', ') : 'General web/academic'}
      Constraints: ${Array.isArray(brief.constraints) ? brief.constraints.join(', ') : 'None'}
      Output Format: ${brief.outputFormat}

      Break this down into a sequence of sub-tasks for a research team consisting of:
      - InformationRetrievalAgent (finds raw info)
      - DataAnalystSynthesizerAgent (analyzes and synthesizes findings)
      - ReportWriterFormatterAgent (writes the final output)
      - QualityAssuranceReviewerAgent (reviews the draft)

      Define each sub-task with:
      1.  'instruction': A clear command for the agent.
      2.  'assignedToAgentRole': The role of the agent (e.g., 'InformationRetrieval').
      3.  'requiredInputs': (Optional) IDs of previous sub-tasks whose output is needed.

      Focus on the flow: Retrieve -> Analyze -> Write -> Review.
      Generate at least one task for each role. For retrieval, consider breaking down by key question or source type if complex.

      IMPORTANT: You must respond with ONLY a valid JSON array. Do not include any explanatory text before or after the JSON.

      Required JSON format:
      [
        {
          "instruction": "string",
          "assignedToAgentRole": "string",
          "requiredInputs": ["optional_array_of_strings"]
        }
      ]
      `;

      const responseJson = await this.processRequest(prompt);
      let subTaskDefs: any[] = [];
      try {
          subTaskDefs = JSON.parse(responseJson);
      } catch (error) {
          console.error(`[${this.name}] Failed to parse decomposition response:`, error);
          // Create basic fallback tasks
          subTaskDefs = [
              { instruction: `Find information on: ${brief.clarifiedScope}`, assignedToAgentRole: 'InformationRetrieval' },
              { instruction: `Analyze retrieved information for key findings`, assignedToAgentRole: 'DataAnalystSynthesizer', requiredInputs: ['subtask_0'] },
              { instruction: `Write a ${brief.outputFormat || 'report'} based on analysis`, assignedToAgentRole: 'ReportWriterFormatter', requiredInputs: ['subtask_1'] },
              { instruction: `Review the draft ${brief.outputFormat || 'report'}`, assignedToAgentRole: 'QualityAssuranceReviewer', requiredInputs: ['subtask_2'] },
          ];
      }

      // Assign placeholder IDs and agent IDs (manager would resolve roles to actual IDs)
      const subTasks: SubTask[] = subTaskDefs.map((def: any, index) => ({
          subTaskId: `subtask_${planId}_${index}`,
          parentTaskId: brief.taskId,
          assignedToAgentId: `agent_for_${def.assignedToAgentRole}`, // Placeholder - Manager needs to map this
          instruction: def.instruction,
          status: 'pending',
          requiredInputs: def.requiredInputs?.map((inputRef: string) => inputRef.replace('subtask_', `subtask_${planId}_`)) || [] // Make IDs unique to plan
      }));

      const plan: ResearchPlan = {
          planId,
          parentTaskId: brief.taskId,
          brief,
          subTasks,
          overallStatus: 'pending'
      };
      console.log(`[${this.name}] Task decomposed into ${subTasks.length} sub-tasks.`);
      return plan;
  }

  /**
   * Assign tasks to agents and monitor (basic implementation)
   */
  async assignAndMonitorTasks(plan: ResearchPlan): Promise<void> {
    console.log(`[${this.name}] Assigning tasks for plan: ${plan.planId}`);

    // In a real system, the Manager would resolve 'agent_for_...' to actual agent IDs
    // We'll simulate sending messages directly using placeholders.
    // Also, dependencies need proper handling (only assign tasks whose inputs are ready).

    for (const subTask of plan.subTasks) {
      // Basic check: Assign only tasks with no pending dependencies (simplistic)
      const inputsReady = (subTask.requiredInputs || []).every(inputId => {
        const inputTask = plan.subTasks.find(st => st.subTaskId === inputId);
        return inputTask?.status === 'completed';
      });

      if (subTask.status === 'pending' && inputsReady) {
        const placeholderAgentId = subTask.assignedToAgentId; // e.g., "agent_for_InformationRetrieval"

        // Resolve placeholder Agent ID to actual agent ID
        let targetAgentId = placeholderAgentId;
        if (placeholderAgentId.startsWith('agent_for_')) {
          const role = placeholderAgentId.replace('agent_for_', '');
          const resolvedId = this.resolveAgentId(role);
          targetAgentId = resolvedId;

          // Update the subtask with the resolved agent ID
          subTask.assignedToAgentId = targetAgentId;
        }

        const metadata = {
          planId: plan.planId,
          subTaskId: subTask.subTaskId,
          parentTaskId: plan.parentTaskId,
          instruction: subTask.instruction,
           // Pass necessary context/data from previous steps if possible
          // requiredData: ... // Fetch results from completed requiredInputs tasks
        };

        console.log(`[${this.name}] Assigning Task ${subTask.subTaskId} to ${targetAgentId} (Resolved from ${placeholderAgentId})`);
        // await this.sendMessage(targetAgentId, `New task assigned: ${subTask.instruction}`, metadata);

        // Update local status - monitoring needs more logic
        subTask.status = 'in-progress'; // Assume assignment starts it
        this.memory.Agent_Response[`plan_${plan.planId}`] = plan; // Update stored plan
      }
    }
     await this.saveMemoryToStorage();
     // TODO: Implement periodic checks for completed sub-tasks to trigger dependent ones
     console.log(`[${this.name}] Initial task assignment phase complete for ${plan.planId}. Monitoring required.`);
  }

  /**
   * Handle completion feedback from QA agent
   */
  async processQaFeedback(feedback: ReviewFeedback): Promise<void> {
      console.log(`[${this.name}] Received QA feedback for task ${feedback.parentTaskId}, draft ${feedback.draftId}`);
      const plan = this.activePlans.get(feedback.parentTaskId) || this.memory.Agent_Response[`plan_${feedback.parentTaskId}`];

      if (!plan) {
          console.error(`[${this.name}] Plan not found for QA feedback: ${feedback.parentTaskId}`);
          return;
      }

      const qaSubTask = plan.subTasks.find((st: SubTask) => st.subTaskId === feedback.reviewId); // Assuming reviewId maps to subTaskId
      if (qaSubTask) qaSubTask.status = 'completed';

      if (feedback.overallStatus === 'approved') {
          console.log(`[${this.name}] Report approved for task ${feedback.parentTaskId}`);
          plan.overallStatus = 'completed';
          // TODO: Retrieve the final report content (how is it passed back?)
          // For now, assume it's accessible via metadata or another message
          const finalReportContent = "Final report content placeholder - requires retrieval mechanism"; // Placeholder
          await this.compileAndDeliverReport(plan.brief, finalReportContent);

      } else if (feedback.overallStatus === 'needs_revision') {
          console.log(`[${this.name}] Report needs revision for task ${feedback.parentTaskId}. Relaying feedback.`);
          // Find the writing sub-task and re-assign/notify the writer agent
          const writerTask = plan.subTasks.find((st: SubTask) => st.assignedToAgentId.includes('ReportWriter')); // Find writer task (needs better linking)
          if (writerTask) {
              writerTask.status = 'pending'; // Re-open the writing task
              // TODO: Resolve writer agent ID
              const writerAgentId = writerTask.assignedToAgentId; // Placeholder
              console.log(`[${this.name}] Sending revision request to ${writerAgentId} (Placeholder ID)`);
              // await this.sendMessage(
              //     writerAgentId,
              //     `Revisions requested for draft ${feedback.draftId}. Feedback: ${feedback.summaryFeedback || 'See checklist'}`,
              //     { ...feedback, type: 'revision_request' }
              // );
          } else {
              console.error(`[${this.name}] Could not find writer task to send revisions.`);
          }
      } else {
           console.log(`[${this.name}] Report rejected for task ${feedback.parentTaskId}`);
           plan.overallStatus = 'failed';
      }

       this.memory.Agent_Response[`plan_${plan.planId}`] = plan; // Update stored plan
       await this.saveMemoryToStorage();
  }


  /**
   * Compile the final report and deliver it
   */
  async compileAndDeliverReport(brief: ResearchTaskBrief, reportContent: string): Promise<void> {
      console.log(`[${this.name}] Compiling final report for task ${brief.taskId}`);
      let finalOutput: FinalResearchReport | Buffer;
      let pdfBuffer: Buffer | undefined;

      const reportTitle = `Research Report: ${brief.topic}`;

      if (brief.outputFormat === 'report' || brief.outputFormat === 'summary') {
          try {
              // Generate PDF version
              const pdfContents: PdfContent[] = [{ title: reportTitle, content: reportContent }];
              const pdfOptions: PdfGenerationOptions = {
                  subtitle: brief.clarifiedScope,
                  saveToByteStore: false, // We want a Buffer for direct delivery
                  category: 'Research Report'
              };

              const result = await this.generatePdf(reportTitle, pdfContents, pdfOptions);

              // Handle the result which could be a Buffer or SavePdfToByteStoreResult
              if (Buffer.isBuffer(result)) {
                  pdfBuffer = result;
              } else {
                  // If it's a SavePdfToByteStoreResult, we need to fetch the PDF from storage
                  // For now, we'll just log the URL and set pdfBuffer to undefined
                  console.log(`[${this.name}] PDF saved to byteStore with ID: ${result.documentId}, URL: ${result.downloadUrl}`);
                  // In a real implementation, you might want to fetch the PDF from the URL
              }

              console.log(`[${this.name}] PDF generated successfully.`);
          } catch (error) {
              console.error(`[${this.name}] Failed to generate PDF:`, error);
          }
      }

      finalOutput = {
          reportId: `report-${brief.taskId}-${Date.now()}`,
          parentTaskId: brief.taskId,
          brief,
          reportTitle,
          reportContent: reportContent, // Keep text version
          generatedPdf: pdfBuffer,
          deliveryTimestamp: new Date(),
      };

      // TODO: Implement actual delivery mechanism (e.g., save to storage, send message back to requester)
      console.log(`[${this.name}] Final report ready for delivery: ${finalOutput.reportId}`);

      // Example: Save to storage
      try {
          await this.saveContent(finalOutput, 'final_research_reports');
          console.log(`[${this.name}] Final report saved to storage.`);
      } catch(error) {
          console.error(`[${this.name}] Failed to save final report:`, error);
      }

      // Remove plan from active memory?
      this.activePlans.delete(brief.taskId); // Or planId if using that map key
      // Optionally clean up long-term memory too
      // delete this.memory.longTerm[`plan_${plan.planId}`];
      // await this.saveMemoryToStorage();
  }

  /**
   * PMO Integration: Analyze a task and recommend appropriate teams
   * This method implements the IStrategicDirectorAgent interface
   */
  async analyzeTask(params: {
    title: string;
    description: string;
    context: string;
    taskType: string;
  }): Promise<{
    success: boolean;
    recommendedTeams?: AgenticTeamId[];
    rationale?: string;
    error?: string;
  }> {
    console.log(`[${this.name}] Analyzing task "${params.title}" for team recommendation`);

    try {
      const prompt = `
      You are a Research Lead and Strategic Director with deep expertise in research methodologies, data analysis, and information gathering. Your primary responsibility is to analyze tasks through a research lens and determine optimal team assignments with a strong bias toward research-driven approaches.

      TASK INFORMATION:
      Title: ${params.title}
      Description: ${params.description}
      Context: ${params.context}
      Task Type: ${params.taskType}

      AVAILABLE TEAMS (Research-Focused Perspective):
      - Ag002 (Research): PRIMARY EXPERTISE - Information gathering, data analysis, market research, competitive intelligence, strategic insights, evidence-based recommendations
      - Ag001 (Marketing): Secondary support - Marketing strategy, campaigns, brand positioning (benefits from research foundation)
      - Ag003 (Software Design): Technical implementation - System architecture, software solutions (requires research for requirements)
      - Ag004 (Sales): Revenue optimization - Sales strategy, customer acquisition (enhanced by market research)
      - Ag005 (Business Analysis): Process analysis - Requirements gathering, strategic planning (strengthened by research insights)

      RESEARCH-FOCUSED ANALYSIS CRITERIA:
      1. Information and data requirements - What research is needed?
      2. Evidence-based decision making - How can research inform the approach?
      3. Knowledge gaps - What unknowns need investigation?
      4. Research methodology - What research methods would be most effective?
      5. Strategic insights - How can research provide competitive advantage?
      6. Cross-team research support - Which teams would benefit from research collaboration?

      RESEARCH LEAD PRIORITIES:
      - Always consider if Research team should lead or support the task
      - Identify opportunities for data-driven insights
      - Ensure evidence-based approaches are prioritized
      - Look for research components even in non-obvious tasks
      - Consider how research can enhance other teams' effectiveness

      Based on the task information, recommend 1-2 teams with Research team involvement when beneficial.
      Provide clear rationale emphasizing research value and methodology.

      Respond in JSON format:
      {
        "recommendedTeams": ["Ag00X", "Ag00Y"],
        "rationale": "Clear explanation emphasizing research value and why these teams are most suitable",
        "researchComponent": "Detailed description of research aspects and methodology",
        "researchValue": "How research will enhance task outcomes and strategic value",
        "confidence": "high|medium|low"
      }
      `;

      const response = await this.processRequest(prompt);

      try {
        const parsed = JSON.parse(response);

        return {
          success: true,
          recommendedTeams: parsed.recommendedTeams || [AgenticTeamId.Research],
          rationale: parsed.rationale || 'Teams selected based on task analysis'
        };
      } catch (parseError) {
        console.error(`[${this.name}] Failed to parse team analysis response:`, parseError);

        // Fallback logic based on task type and description
        const fallbackTeams = this.getFallbackTeamRecommendation(params);

        return {
          success: true,
          recommendedTeams: fallbackTeams.teams,
          rationale: fallbackTeams.rationale
        };
      }
    } catch (error: any) {
      console.error(`[${this.name}] Error analyzing task:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Fallback team recommendation logic with strong research focus
   */
  private getFallbackTeamRecommendation(params: {
    title: string;
    description: string;
    context: string;
    taskType: string;
  }): { teams: AgenticTeamId[]; rationale: string } {
    const title = params.title.toLowerCase();
    const description = params.description.toLowerCase();
    const combined = `${title} ${description}`.toLowerCase();

    // Primary research-focused keywords - Research team leads
    if (combined.match(/research|analysis|data|study|investigation|survey|market research|competitive intelligence|insights|evidence|methodology/)) {
      return {
        teams: [AgenticTeamId.Research],
        rationale: 'Task contains core research and analysis components requiring Research Team leadership with evidence-based methodology and data-driven insights'
      };
    }

    // Marketing keywords - Research team supports with market intelligence
    if (combined.match(/marketing|campaign|brand|promotion|advertising|content|positioning/)) {
      return {
        teams: [AgenticTeamId.Marketing, AgenticTeamId.Research],
        rationale: 'Marketing task requiring Research Team collaboration for market intelligence, competitive analysis, and data-driven strategy development'
      };
    }

    // Technical keywords - Research team provides requirements analysis
    if (combined.match(/software|development|technical|system|application|code|architecture/)) {
      return {
        teams: [AgenticTeamId.SoftwareDesign, AgenticTeamId.Research],
        rationale: 'Technical development task enhanced by Research Team involvement for requirements analysis, user research, and technology assessment'
      };
    }

    // Sales keywords - Research team provides market and customer insights
    if (combined.match(/sales|revenue|customer acquisition|lead generation|sales process|conversion/)) {
      return {
        teams: [AgenticTeamId.Sales, AgenticTeamId.Research],
        rationale: 'Sales-focused task requiring Research Team support for market analysis, customer intelligence, and performance metrics to optimize sales strategy'
      };
    }

    // Business analysis keywords - Research team provides data foundation
    if (combined.match(/business process|requirements|strategy|planning|optimization|workflow/)) {
      return {
        teams: [AgenticTeamId.BusinessAnalysis, AgenticTeamId.Research],
        rationale: 'Business analysis task strengthened by Research Team collaboration for data analysis, process research, and evidence-based strategic recommendations'
      };
    }

    // Strategic planning keywords - Research team leads with strategic insights
    if (combined.match(/strategic|planning|roadmap|vision|objectives|goals|direction/)) {
      return {
        teams: [AgenticTeamId.Research],
        rationale: 'Strategic planning task requiring Research Team leadership for comprehensive analysis, market intelligence, and evidence-based strategic recommendations'
      };
    }

    // Default to Research team with comprehensive rationale
    return {
      teams: [AgenticTeamId.Research],
      rationale: 'Task assigned to Research Team for comprehensive analysis, information gathering, and evidence-based approach to ensure optimal outcomes through research-driven methodology'
    };
  }

  /**
   * PMO Integration: Retrieve PMO tasks and assessment details (DELEGATED)
   * ResearchLead coordinates PMO document retrieval by delegating to InformationRetrievalAgent
   */
  async retrievePMOTasks(
    pmoId: string,
    userRequest: string,
    modelNameOrProvider?: string
  ): Promise<{
    success: boolean;
    tasks: string[];
    assessment: string;
    requirements: string;
    error?: string;
  }> {
    console.log(`[${this.name}] Coordinating PMO task retrieval for PMO ID: ${pmoId}`);

    try {
      // First, try to retrieve PMO record directly from Firestore (coordination task)
      let pmoRecord = null;
      try {
        const pmoDoc = await adminDb.collection('pmo_records').doc(pmoId).get();
        if (pmoDoc.exists) {
          pmoRecord = pmoDoc.data();
          console.log(`[${this.name}] Found PMO record in Firestore`);
        }
      } catch (firestoreError) {
        console.warn(`[${this.name}] Could not retrieve PMO record from Firestore:`, firestoreError);
      }

      // Delegate PMO document analysis to InformationRetrievalAgent
      const retrievalTask = {
        instruction: `Retrieve and analyze PMO documentation for ${pmoId}`,
        searchQuery: `PMO ${pmoId} ${userRequest} tasks requirements assessment`,
        analysisType: 'structured_extraction',
        pmoId,
        userRequest,
        pmoRecord,
        extractionRequirements: [
          'Identify specific actionable tasks',
          'Extract PMO assessment and analysis',
          'Gather detailed requirements and specifications',
          'Focus on research requirements and strategic objectives'
        ]
      };

      const delegationResult = await this.delegateToAgent('info-retriever', 'analyzePMODocuments', retrievalTask);

      if (!this.validateDelegationResult(delegationResult, 'PMO document analysis')) {
        console.warn(`[${this.name}] PMO document analysis delegation failed, using fallback`);
        return this.generateFallbackPMOTasks(pmoId, userRequest, pmoRecord);
      }

      console.log(`[${this.name}] PMO document analysis successfully delegated to InformationRetrievalAgent`);

      // In a real implementation, this would return the actual analysis from the specialist agent
      // For now, return a structured response indicating successful delegation
      return {
        success: true,
        tasks: [
          `PMO Analysis: ${pmoId}`,
          'Document retrieval and analysis (delegated)',
          'Requirements extraction (delegated)',
          'Strategic assessment (delegated)'
        ],
        assessment: `PMO document analysis delegated to InformationRetrievalAgent for comprehensive review of ${pmoId}`,
        requirements: `Detailed requirements analysis delegated to specialist agent. PMO context: ${userRequest}`
      };

    } catch (error: any) {
      console.error(`[${this.name}] Error coordinating PMO task retrieval:`, error);
      return {
        success: false,
        tasks: [],
        assessment: '',
        requirements: '',
        error: error.message
      };
    }
  }

  /**
   * Fallback PMO task generation when delegation fails
   */
  private generateFallbackPMOTasks(pmoId: string, userRequest: string, pmoRecord: any): {
    success: boolean;
    tasks: string[];
    assessment: string;
    requirements: string;
  } {
    console.log(`[${this.name}] Using fallback PMO task generation`);

    return {
      success: true,
      tasks: pmoRecord ?
        [`Analyze: ${pmoRecord.title}`, 'Research requirements', 'Develop implementation strategy'] :
        ['PMO task analysis required', 'Document retrieval needed', 'Requirements gathering required'],
      assessment: pmoRecord?.pmoAssessment || 'PMO assessment delegation failed - requires InformationRetrievalAgent implementation',
      requirements: `PMO requirements analysis needed for ${pmoId}. Context: ${userRequest}`
    };
  }

  /**
   * PMO Integration: Create a strategic implementation plan based on PMO requirements
   * This method follows the same workflow as StrategicDirectorAgent but with research focus
   */
  async createPMOStrategicPlan(params: {
    pmoId: string;
    projectTitle: string;
    projectDescription: string;
    pmoAssessment: string;
    teamSelectionRationale: string;
    priority: string;
    category: string;
    requirementsDocument?: string;
  }): Promise<{
    success: boolean;
    strategicPlan?: string;
    documentTitle?: string;
    documentUrl?: string;
    error?: string;
  }> {
    try {
      console.log(`[${this.name}] Creating strategic implementation plan for PMO project: ${params.projectTitle}`);

      // Retrieve additional PMO context
      const pmoContext = await this.retrievePMOTasks(params.pmoId, params.projectDescription);

      // Generate comprehensive strategic plan with research focus (delegated from ResearchAgentManager)
      const strategicPlanPrompt = `
      You are a Research Lead Agent working under the coordination of the ResearchAgentManager. You have been delegated to create a comprehensive strategic implementation plan for a PMO project.

      CRITICAL INSTRUCTION: This is a PMO-delegated research task coordinated by ResearchAgentManager. Your role is to BUILD UPON and ELABORATE the detailed PMO assessment provided below. Do not start from scratch - use the PMO analysis as your foundation and enhance it with additional research insights and strategic recommendations.

      PROJECT INFORMATION:
      Title: ${params.projectTitle}
      Description: ${params.projectDescription}
      Priority: ${params.priority}
      Category: ${params.category}

      COMPREHENSIVE PMO ASSESSMENT TO BUILD UPON:
      ${params.pmoAssessment}

      TEAM SELECTION RATIONALE:
      ${params.teamSelectionRationale}

      ADDITIONAL PMO CONTEXT:
      Tasks: ${pmoContext.tasks.join(', ')}
      Assessment: ${pmoContext.assessment}
      Requirements: ${pmoContext.requirements}

      ${params.requirementsDocument ? `Requirements Document Available: ${params.requirementsDocument}` : ''}

      RESEARCH ENHANCEMENT OBJECTIVES:
      Your strategic plan must ELABORATE and ENHANCE the PMO assessment above by:
      1. Building upon every aspect identified in the PMO analysis
      2. Adding detailed research findings and data to support PMO conclusions
      3. Expanding strategic recommendations based on PMO requirements
      4. Providing implementation guidance that aligns with PMO objectives
      5. Enhancing the PMO analysis with additional insights and strategic depth

      STRATEGIC PLAN REQUIREMENTS:
      Create a comprehensive strategic implementation plan that includes:

      1. EXECUTIVE SUMMARY
         - Build upon PMO project overview with enhanced strategic analysis
         - Elaborate on PMO-identified objectives with detailed research insights
         - Expand PMO methodology with comprehensive research approach
         - Enhance PMO findings with additional strategic importance assessment

      2. ENHANCED RESEARCH STRATEGY (Building on PMO Assessment)
         - Elaborate on PMO-identified information requirements with detailed research plan
         - Expand PMO data sources with additional research methods and tools
         - Build upon PMO analysis frameworks with advanced research methodologies
         - Enhance PMO quality measures with comprehensive assurance protocols

      3. STRATEGIC IMPLEMENTATION ROADMAP (PMO-Aligned)
         - Develop detailed execution plan based on PMO requirements and timeline
         - Elaborate PMO milestones with comprehensive phase-by-phase approach
         - Enhance PMO resource allocation with detailed research resource planning
         - Build upon PMO risk assessment with advanced mitigation strategies

      4. CROSS-TEAM COORDINATION (PMO Framework Enhancement)
         - Expand PMO team integration recommendations with detailed collaboration protocols
         - Build upon PMO communication requirements with comprehensive frameworks
         - Enhance PMO coordination guidelines with advanced monitoring systems
         - Elaborate PMO progress tracking with detailed reporting mechanisms

      5. ENHANCED DELIVERABLES AND OUTCOMES (PMO Requirements Plus)
         - Build upon PMO-specified outputs with additional research deliverables
         - Elaborate PMO strategic recommendations with detailed implementation guidance
         - Enhance PMO guidelines with comprehensive best practices and methodologies
         - Expand PMO success metrics with advanced measurement and evaluation frameworks

      6. COMPREHENSIVE QUALITY ASSURANCE (PMO Standards Plus)
         - Build upon PMO review processes with enhanced validation protocols
         - Elaborate PMO validation methods with comprehensive quality frameworks
         - Enhance PMO improvement processes with advanced continuous enhancement
         - Expand PMO stakeholder feedback with detailed integration mechanisms

      CRITICAL REQUIREMENTS:
      - Every section must reference and build upon the specific PMO assessment provided above
      - Include direct quotes and references to PMO findings throughout the plan
      - Demonstrate how each recommendation enhances and elaborates the PMO analysis
      - Maintain complete alignment with PMO objectives while adding research depth
      - Format as a comprehensive markdown document with clear PMO-research integration
      `;

      const strategicPlan = await this.processRequest(strategicPlanPrompt);

      // Generate document title
      const documentTitle = `Strategic Implementation Plan - ${params.projectTitle} - ${params.pmoId}`;

      // Save the strategic plan to storage (both user-specific and global for PMO integration)
      try {
        // Save to user-specific collection (existing functionality)
        const documentId = await this.saveContent({
          title: documentTitle,
          content: strategicPlan,
          pmoId: params.pmoId,
          projectTitle: params.projectTitle,
          category: params.category,
          priority: params.priority,
          createdBy: this.name,
          agentType: 'ResearchLeadAgent',
          documentType: 'strategic_implementation_plan'
        }, 'pmo_strategic_plans');

        const documentUrl = `services/pmo/${documentId}`;

        // Also save to global Agent_Output collection for PMO integration
        try {
          const { v4: uuidv4 } = await import('uuid');
          const { adminDb } = await import('../../../components/firebase-admin');

          const requestId = uuidv4();

          // Prepare data for global storage (matching marketing workflow format)
          const agentOutputData = {
            requestId,
            timestamp: new Date(),
            agentType: 'ResearchLeadAgent', // Research Lead Agent working under ResearchAgentManager coordination
            userId: this.userId,
            title: params.projectTitle, // Use project title as main title (like Marketing does)
            prompt: `Create strategic implementation plan for PMO project: ${params.projectTitle}`,
            result: {
              thinking: '', // Research strategic planning doesn't expose thinking process
              output: strategicPlan,
              documentUrl: documentUrl
            },
            agentMessages: [], // Research strategic planning uses internal coordination
            modelInfo: {
              provider: 'anthropic',
              model: 'claude-sonnet-4-20250514'
            },
            // PMO-specific metadata for proper integration (matching marketing pattern)
            pmoMetadata: {
              pmoId: params.pmoId,
              teamId: 'Research',
              teamName: 'Research',
              source: 'PMO',
              processedAt: new Date().toISOString(),
              documentType: 'strategic_implementation_plan',
              autoTriggered: true
            },
            // Include metadata for PMO Output tab (matching marketing pattern)
            metadata: {
              recordTitle: params.projectTitle, // Use project title as record title
              projectTitle: params.projectTitle,
              source: 'PMO',
              pmoId: params.pmoId,
              teamName: 'Research',
              teamId: 'Research',
              outputType: 'strategic_plan'
            },
            // Include category for filtering (matching marketing pattern)
            category: params.category || `PMO - ${params.projectTitle} - ${params.pmoId}`,
            // Include context information
            contextOptions: {
              customContext: null,
              documentReferences: null,
              category: params.category,
              pmoContext: {
                pmoId: params.pmoId,
                projectTitle: params.projectTitle,
                priority: params.priority
              }
            }
          };

          console.log(`[${this.name}] Storing research strategic plan in global collection with requestId: ${requestId}`);
          await adminDb.collection('Agent_Output').doc(requestId).set(agentOutputData);
          console.log(`[${this.name}] Successfully stored research strategic plan in global collection`);
        } catch (globalStorageError) {
          console.error(`[${this.name}] Failed to save strategic plan to global collection:`, globalStorageError);
          // Continue even if global storage fails
        }

        console.log(`[${this.name}] Strategic plan saved with ID: ${documentId}`);

        return {
          success: true,
          strategicPlan,
          documentTitle,
          documentUrl
        };
      } catch (saveError: any) {
        console.error(`[${this.name}] Failed to save strategic plan:`, saveError);

        // Return plan even if save failed
        return {
          success: true,
          strategicPlan,
          documentTitle,
          error: `Plan generated but save failed: ${saveError.message}`
        };
      }
    } catch (error: any) {
      console.error(`[${this.name}] Error creating PMO strategic plan:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create comprehensive strategic task collection from any context
   * This is the main method for strategic task creation with research focus
   */
  async createStrategicTaskCollection(context: TaskGenerationContext): Promise<StrategicTaskCollection> {
    const collectionId = `research-strategic-collection-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const currentDate = new Date();

    console.log(`[${this.name}] Creating strategic task collection: ${collectionId}`);

    try {
      // Generate strategic tasks using LLM
      const strategicTasks = await this.createStrategicPlanningTasks(context);

      // Calculate overall timeline
      const estimatedEndDate = new Date(currentDate);
      estimatedEndDate.setDate(estimatedEndDate.getDate() + 30); // Default 30-day timeline

      // Analyze team assignments
      const teamAssignments: { [teamName: string]: { taskCount: number; taskIds: string[]; estimatedWorkload: string } } = {};

      strategicTasks.forEach(task => {
        const teamName = task.assignedTeam;
        if (!teamAssignments[teamName]) {
          teamAssignments[teamName] = {
            taskCount: 0,
            taskIds: [],
            estimatedWorkload: '0 days'
          };
        }
        teamAssignments[teamName].taskCount++;
        teamAssignments[teamName].taskIds.push(task.id);
      });

      // Calculate workload estimates
      Object.keys(teamAssignments).forEach(teamName => {
        const taskCount = teamAssignments[teamName].taskCount;
        const estimatedDays = Math.ceil(taskCount * 2.5); // Estimate 2.5 days per task
        teamAssignments[teamName].estimatedWorkload = `${estimatedDays} days`;
      });

      const collection: StrategicTaskCollection = {
        collectionId,
        name: context.pmoContext ? `PMO Project: ${context.pmoContext.projectTitle}` : `Research Strategic Collection`,
        description: context.userRequest || 'Strategic task collection for research and analysis',
        source: context.pmoContext ? 'PMO Project' : 'Research Project',
        totalTasks: strategicTasks.length,
        tasks: strategicTasks,
        overallTimeline: {
          startDate: currentDate,
          estimatedEndDate,
          criticalPath: strategicTasks.filter(t => t.priority === 'CRITICAL').map(t => t.id)
        },
        teamAssignments,
        successMetrics: [
          'All tasks completed within timeline',
          'Quality standards met for all deliverables',
          'Stakeholder satisfaction achieved',
          'Research objectives fulfilled'
        ],
        createdAt: currentDate,
        updatedAt: currentDate
      };

      // Store the collection
      this.activeStrategicCollections.set(collectionId, collection);

      // Save to memory
      this.memory.Agent_Response[`strategic_collection_${collectionId}`] = collection;
      await this.saveMemoryToStorage();

      console.log(`[${this.name}] Strategic task collection created with ${strategicTasks.length} tasks`);

      return collection;
    } catch (error: any) {
      console.error(`[${this.name}] Error creating strategic task collection:`, error);

      // Return minimal collection on error
      return {
        collectionId,
        name: 'Error Collection',
        description: 'Failed to generate strategic tasks',
        source: 'Research Project',
        totalTasks: 0,
        tasks: [],
        overallTimeline: {
          startDate: currentDate,
          estimatedEndDate: currentDate,
          criticalPath: []
        },
        teamAssignments: {},
        successMetrics: [],
        createdAt: currentDate,
        updatedAt: currentDate
      };
    }
  }

  /**
   * Create strategic planning tasks (DELEGATED)
   * ResearchLead coordinates strategic task creation by delegating to DataAnalystSynthesizerAgent
   */
  private async createStrategicPlanningTasks(context: TaskGenerationContext): Promise<StrategicTask[]> {
    console.log(`[${this.name}] Coordinating strategic task creation`);

    try {
      // Delegate strategic task creation to DataAnalystSynthesizerAgent
      const strategicPlanningTask = {
        instruction: 'Create comprehensive strategic task breakdown with research focus',
        context,
        taskCategories: [
          'Research (PRIMARY)',
          'Market Intelligence',
          'Customer Intelligence',
          'Strategic Planning',
          'Performance Metrics',
          'Customer Validation'
        ],
        teamAssignments: [
          'Research Team (PRIMARY)',
          'Business Analysis Team',
          'Marketing Team',
          'Sales Team',
          'Software Design Team',
          'Content Team'
        ],
        requirements: [
          'ALWAYS start with comprehensive research and information gathering',
          'Ensure evidence-based methodology in all tasks',
          'Prioritize Research Team leadership or collaboration in most tasks',
          'Include data analysis and validation components',
          'Focus on strategic insights derived from research',
          'Emphasize quality assurance through research validation'
        ],
        taskCount: '6-8 strategic tasks',
        researchFocus: true
      };

      const delegationResult = await this.delegateToAgent('data-analyst', 'createStrategicTasks', strategicPlanningTask);

      if (!this.validateDelegationResult(delegationResult, 'strategic task creation')) {
        console.warn(`[${this.name}] Strategic task creation delegation failed, using fallback`);
        return await this.generateFallbackStrategicTasks(context);
      }

      console.log(`[${this.name}] Strategic task creation successfully delegated to DataAnalystSynthesizerAgent`);

      // In a real implementation, this would return the actual tasks from the specialist agent
      // For now, return structured tasks indicating successful delegation
      return this.generateDelegatedStrategicTasks(context);

    } catch (error: any) {
      console.error(`[${this.name}] Error coordinating strategic task creation:`, error);
      return await this.generateFallbackStrategicTasks(context);
    }
  }

  /**
   * Generate delegated strategic tasks structure
   */
  private generateDelegatedStrategicTasks(context: TaskGenerationContext): StrategicTask[] {
    const baseTaskId = Date.now();
    const tasks: StrategicTask[] = [
      {
        id: `delegated-task-${baseTaskId}-1`,
        title: 'Primary Research & Data Collection (DELEGATED)',
        description: 'Comprehensive information gathering and data collection delegated to Research Team specialists',
        category: 'Research',
        priority: 'CRITICAL',
        assignedTeam: 'Research Team',
        status: 'IDENTIFIED',
        specificRequirements: [
          'Task creation delegated to DataAnalystSynthesizerAgent',
          'Research methodology to be defined by specialist',
          'Data sources identification by InformationRetrievalAgent'
        ],
        deliverable: 'Research data collection plan and initial findings',
        timeline: { estimatedDuration: '5 days' },
        dependencies: [],
        blockers: [],
        successCriteria: ['Delegation successful', 'Research plan created', 'Data collection initiated'],
        resources: {
          tools: ['Research tools', 'Analysis software'],
          personnel: ['Research Team'],
          documents: []
        },
        metadata: {
          createdAt: new Date(),
          createdBy: this.name,
          updatedAt: new Date(),
          source: 'Strategic Planning',
          pmoId: context.pmoContext?.pmoId,
          projectId: context.pmoContext?.projectTitle,
          requestId: context.userRequest
        }
      }
    ];

    return tasks;
  }

  /**
   * Generate fallback strategic tasks when delegation fails
   */
  private async generateFallbackStrategicTasks(context: TaskGenerationContext): Promise<StrategicTask[]> {
    const prompt = `
    You are a Research Lead and Strategic Director with deep expertise in research methodologies, data analysis, and evidence-based decision making. Create strategic tasks that prioritize research excellence and data-driven approaches.

    RESEARCH-FOCUSED CONTEXT:
    User Request: ${context.userRequest || 'Strategic research planning required'}
    Analysis Content: ${context.analysisContent || 'Requires comprehensive research analysis'}
    Strategic Objectives: ${context.strategicObjectives?.join(', ') || 'Evidence-based strategic objectives to be determined through research'}
    Constraints: ${context.constraints?.join(', ') || 'Research methodology and data quality standards must be maintained'}
    Research Scope: ${context.researchScope || 'Comprehensive multi-dimensional analysis'}
    Research Questions: ${context.researchQuestions?.join(', ') || 'Research questions to be defined through preliminary investigation'}

    ${context.pmoContext ? `PMO CONTEXT (Research Perspective):
    PMO ID: ${context.pmoContext.pmoId}
    Project Title: ${context.pmoContext.projectTitle}
    Project Description: ${context.pmoContext.projectDescription}
    Priority: ${context.pmoContext.priority}
    Category: ${context.pmoContext.category}
    Research Requirements: Comprehensive analysis and evidence-based recommendations required` : ''}

    RESEARCH-PRIORITIZED TASK CATEGORIES:
    - Research (PRIMARY): Information gathering, data analysis, competitive intelligence, market research
    - Market Intelligence: Industry analysis, trend identification, competitive landscape
    - Customer Intelligence: User research, behavior analysis, needs assessment
    - Strategic Planning: Evidence-based strategy development, research-informed planning
    - Performance Metrics: Data collection, analysis frameworks, measurement systems
    - Customer Validation: Research validation, hypothesis testing, evidence gathering

    AVAILABLE TEAMS (Research-Focused Assignments):
    - Research Team (PRIMARY): Lead all research initiatives, data analysis, strategic insights
    - Business Analysis Team: Support with process research and requirements analysis
    - Marketing Team: Collaborate on market research and customer intelligence
    - Sales Team: Provide customer insights and market feedback for research
    - Software Design Team: Support with technical research and user experience analysis
    - Content Team: Document research findings and create research-based content

    RESEARCH LEAD TASK CREATION PRIORITIES:
    1. ALWAYS start with comprehensive research and information gathering
    2. Ensure evidence-based methodology in all tasks
    3. Prioritize Research Team leadership or collaboration in most tasks
    4. Include data analysis and validation components
    5. Focus on strategic insights derived from research
    6. Emphasize quality assurance through research validation

    Create 6-8 strategic tasks with STRONG RESEARCH FOCUS that cover:
    1. Primary Research & Data Collection (Research Team leads)
    2. Market Intelligence & Competitive Analysis (Research Team leads)
    3. Customer Intelligence & Behavior Analysis (Research Team leads)
    4. Strategic Analysis & Evidence-Based Planning (Research Team leads)
    5. Cross-Team Research Collaboration (Research Team coordinates)
    6. Research Validation & Quality Assurance (Research Team validates)
    7. Implementation Research & Monitoring (Research Team supports)
    8. Research Documentation & Knowledge Management (Research Team documents)

    Return JSON array of tasks with:
    - title: Clear, research-focused actionable task title
    - description: Detailed task description emphasizing research methodology
    - category: Prioritize Research, Market Intelligence, Customer Intelligence categories
    - priority: CRITICAL|HIGH|MEDIUM|LOW (bias toward HIGH for research tasks)
    - assignedTeam: Prioritize "Research Team" or include Research Team collaboration
    - timeline: { estimatedDuration: "X days" } (allow adequate time for thorough research)
    - specificRequirements: Array emphasizing research methodology and data quality
    - deliverable: Research-focused outputs (reports, analysis, insights, recommendations)
    - successCriteria: Include research quality, evidence validation, and insight generation
    - dependencies: Consider research workflow and data dependencies

    ENSURE: Research Team is involved in majority of tasks, either leading or collaborating.
    Focus on research excellence, evidence-based insights, and strategic value through data-driven approaches.
    `;

    try {
      const response = await this.processRequest(prompt);
      const parsed = JSON.parse(response);

      return parsed.map((taskData: any, index: number) => {
        const taskId = `task-${Date.now()}-${index}`;

        return {
          id: taskId,
          title: taskData.title || `Strategic Task ${index + 1}`,
          description: taskData.description || 'Task description to be defined',
          category: taskData.category || 'Research',
          priority: taskData.priority || 'MEDIUM',
          assignedTeam: taskData.assignedTeam || 'Research Team',
          status: 'IDENTIFIED',
          specificRequirements: taskData.specificRequirements || [],
          deliverable: taskData.deliverable || 'Task deliverable to be defined',
          timeline: {
            estimatedDuration: taskData.timeline?.estimatedDuration || '3 days',
            startDate: undefined,
            dueDate: undefined,
            milestones: []
          },
          dependencies: taskData.dependencies || [],
          blockers: [],
          successCriteria: taskData.successCriteria || ['Task completed successfully'],
          resources: {
            tools: ['Research tools', 'Analysis software'],
            personnel: [taskData.assignedTeam],
            documents: []
          },
          metadata: {
            createdAt: new Date(),
            createdBy: this.name,
            updatedAt: new Date(),
            source: context.pmoContext ? 'PMO Requirements' : 'Strategic Planning',
            pmoId: context.pmoContext?.pmoId,
            projectId: context.pmoContext?.projectTitle,
            requestId: context.userRequest
          }
        } as StrategicTask;
      });
    } catch (error: any) {
      console.error(`[${this.name}] Error generating strategic tasks:`, error);

      // Return fallback tasks
      return [
        {
          id: `fallback-task-${Date.now()}`,
          title: 'Comprehensive Research Analysis',
          description: 'Conduct comprehensive research and analysis based on project requirements',
          category: 'Research',
          priority: 'HIGH',
          assignedTeam: 'Research Team',
          status: 'IDENTIFIED',
          specificRequirements: ['Define research scope', 'Identify data sources', 'Establish methodology'],
          deliverable: 'Research analysis report',
          timeline: {
            estimatedDuration: '5 days'
          },
          dependencies: [],
          blockers: [],
          successCriteria: ['Research completed', 'Analysis documented', 'Recommendations provided'],
          resources: {
            tools: ['Research tools'],
            personnel: ['Research Team'],
            documents: []
          },
          metadata: {
            createdAt: new Date(),
            createdBy: this.name,
            updatedAt: new Date(),
            source: 'System Generated',
            pmoId: context.pmoContext?.pmoId,
            projectId: context.pmoContext?.projectTitle
          }
        }
      ];
    }
  }

  /**
   * Delegation Infrastructure: Delegate tasks to specialist agents
   */
  private async delegateToAgent(agentRole: string, method: string, taskData: any): Promise<any> {
    console.log(`[${this.name}] Delegating ${method} to ${agentRole}`);

    const agentId = this.resolveAgentId(agentRole);
    const taskMetadata = {
      delegatedBy: this.id,
      delegationTimestamp: new Date(),
      taskType: method,
      instruction: taskData.instruction || `Execute ${method}`,
      ...taskData
    };

    try {
      // In a real implementation, this would send a message to the actual agent
      // For now, we'll simulate the delegation and return a structured response
      console.log(`[${this.name}] Task delegated to ${agentId}: ${method}`);

      // TODO: Replace with actual agent communication
      // return await this.sendMessage(agentId, `Delegated task: ${method}`, taskMetadata);

      // Temporary simulation response
      return {
        success: true,
        agentId,
        method,
        taskData,
        delegatedAt: new Date(),
        status: 'delegated'
      };
    } catch (error: any) {
      console.error(`[${this.name}] Failed to delegate to ${agentRole}:`, error);
      throw new Error(`Delegation failed: ${error.message}`);
    }
  }

  /**
   * Resolve agent role to actual agent ID
   */
  private resolveAgentId(agentRole: string): string {
    const agentId = this.agentRoleMapping.get(agentRole);
    if (!agentId) {
      console.warn(`[${this.name}] No agent ID mapping found for role: ${agentRole}`);
      return agentRole; // Fallback to role name
    }
    return agentId;
  }

  /**
   * Validate delegation results and structure response
   */
  private validateDelegationResult(result: any, expectedType: string): boolean {
    if (!result || !result.success) {
      console.error(`[${this.name}] Delegation result validation failed for ${expectedType}`);
      return false;
    }
    return true;
  }

  /**
   * Determine research request type using Gemini 2.5 Pro
   * This method analyzes the research request to classify the type of research needed
   */
  async determineResearchRequestType(request: {
    title: string;
    description: string;
    context?: string;
    scope?: string;
  }): Promise<{
    success: boolean;
    researchType: 'user_research' | 'feasibility_study' | 'algorithmic_exploration' | 'experimental_design' | 'qualitative_analysis' | 'quantitative_analysis' | 'insight_synthesis' | 'market_research' | 'competitive_analysis' | 'technical_research';
    methodology: string[];
    recommendedAgent: string;
    rationale: string;
    estimatedDuration: string;
    error?: string;
  }> {
    console.log(`[${this.name}] Determining research request type for: ${request.title}`);

    try {
      const analysisPrompt = `
      You are a Research Lead with expertise in research methodologies and study design. Analyze the following research request to determine the most appropriate research type and methodology.

      RESEARCH REQUEST:
      Title: ${request.title}
      Description: ${request.description}
      Context: ${request.context || 'Not specified'}
      Scope: ${request.scope || 'Not specified'}

      RESEARCH TYPE CATEGORIES:
      1. USER_RESEARCH: User interviews, surveys, behavioral analysis, needs assessment, user journey mapping
      2. FEASIBILITY_STUDY: Technical feasibility, market viability, resource assessment, risk analysis
      3. ALGORITHMIC_EXPLORATION: Algorithm research, computational methods, technical implementation analysis
      4. EXPERIMENTAL_DESIGN: A/B testing, hypothesis testing, controlled experiments, statistical design
      5. QUALITATIVE_ANALYSIS: Interview analysis, thematic coding, narrative synthesis, content analysis
      6. QUANTITATIVE_ANALYSIS: Statistical analysis, data modeling, metric calculation, trend analysis
      7. INSIGHT_SYNTHESIS: Cross-data synthesis, strategic recommendations, actionable insights generation
      8. MARKET_RESEARCH: Industry analysis, market sizing, trend identification, competitive landscape
      9. COMPETITIVE_ANALYSIS: Competitor research, feature comparison, market positioning analysis
      10. TECHNICAL_RESEARCH: Technology assessment, technical documentation review, system analysis

      SPECIALIST AGENT ASSIGNMENTS:
      - InformationRetrievalAgent: Data gathering, document analysis, source identification
      - DataAnalystSynthesizerAgent: Data analysis, statistical work, synthesis, insights generation
      - ReportWriterFormatterAgent: Documentation, report creation, presentation formatting
      - QualityAssuranceReviewerAgent: Validation, quality control, methodology review

      Analyze the request and respond in JSON format:
      {
        "researchType": "one of the categories above in lowercase with underscores",
        "methodology": ["list of specific research methods to be used"],
        "recommendedAgent": "primary specialist agent for this research type",
        "rationale": "detailed explanation of why this research type and agent assignment",
        "estimatedDuration": "estimated time in days",
        "confidence": "high|medium|low"
      }

      Focus on matching the request to the most appropriate research methodology and specialist agent capabilities.
      `;

      // Use Gemini 2.5 Pro for advanced research type analysis
      const response = await this.processRequest(analysisPrompt, 'gemini-2.5-flash-exp');

      try {
        const parsed = JSON.parse(response);

        return {
          success: true,
          researchType: parsed.researchType || 'insight_synthesis',
          methodology: parsed.methodology || ['Comprehensive analysis'],
          recommendedAgent: parsed.recommendedAgent || 'DataAnalystSynthesizerAgent',
          rationale: parsed.rationale || 'Research type analysis completed',
          estimatedDuration: parsed.estimatedDuration || '3-5 days'
        };
      } catch (parseError) {
        console.error(`[${this.name}] Failed to parse research type analysis:`, parseError);

        // Fallback classification based on keywords
        return this.classifyResearchRequestFallback(request);
      }
    } catch (error: any) {
      console.error(`[${this.name}] Error determining research request type:`, error);
      return {
        success: false,
        researchType: 'insight_synthesis',
        methodology: [],
        recommendedAgent: 'DataAnalystSynthesizerAgent',
        rationale: 'Error in research type analysis',
        estimatedDuration: '3-5 days',
        error: error.message
      };
    }
  }

  /**
   * Fallback research request classification using keyword analysis
   */
  private classifyResearchRequestFallback(request: {
    title: string;
    description: string;
    context?: string;
    scope?: string;
  }): {
    success: boolean;
    researchType: 'user_research' | 'feasibility_study' | 'algorithmic_exploration' | 'experimental_design' | 'qualitative_analysis' | 'quantitative_analysis' | 'insight_synthesis' | 'market_research' | 'competitive_analysis' | 'technical_research';
    methodology: string[];
    recommendedAgent: string;
    rationale: string;
    estimatedDuration: string;
  } {
    const combined = `${request.title} ${request.description} ${request.context || ''} ${request.scope || ''}`.toLowerCase();

    // User research keywords
    if (combined.match(/user|customer|interview|survey|behavior|needs|journey|persona|usability/)) {
      return {
        success: true,
        researchType: 'user_research',
        methodology: ['User interviews', 'Surveys', 'Behavioral analysis'],
        recommendedAgent: 'InformationRetrievalAgent',
        rationale: 'Request contains user research indicators requiring primary data collection',
        estimatedDuration: '5-7 days'
      };
    }

    // Feasibility study keywords
    if (combined.match(/feasibility|viability|assessment|risk|resource|implementation|cost|timeline/)) {
      return {
        success: true,
        researchType: 'feasibility_study',
        methodology: ['Technical assessment', 'Resource analysis', 'Risk evaluation'],
        recommendedAgent: 'DataAnalystSynthesizerAgent',
        rationale: 'Request requires feasibility analysis and assessment capabilities',
        estimatedDuration: '4-6 days'
      };
    }

    // Market research keywords
    if (combined.match(/market|industry|trend|size|segment|opportunity|landscape|growth/)) {
      return {
        success: true,
        researchType: 'market_research',
        methodology: ['Market analysis', 'Industry research', 'Trend identification'],
        recommendedAgent: 'InformationRetrievalAgent',
        rationale: 'Request focuses on market intelligence and industry analysis',
        estimatedDuration: '4-5 days'
      };
    }

    // Competitive analysis keywords
    if (combined.match(/competitor|competitive|comparison|benchmark|positioning|alternative/)) {
      return {
        success: true,
        researchType: 'competitive_analysis',
        methodology: ['Competitor research', 'Feature comparison', 'Market positioning'],
        recommendedAgent: 'InformationRetrievalAgent',
        rationale: 'Request requires competitive intelligence and comparison analysis',
        estimatedDuration: '3-4 days'
      };
    }

    // Default to insight synthesis
    return {
      success: true,
      researchType: 'insight_synthesis',
      methodology: ['Comprehensive analysis', 'Data synthesis', 'Strategic insights'],
      recommendedAgent: 'DataAnalystSynthesizerAgent',
      rationale: 'General research request requiring synthesis and analysis capabilities',
      estimatedDuration: '3-5 days'
    };
  }

  /**
   * Generate charts for research visualization (DELEGATED)
   * ResearchLead coordinates chart generation by delegating to DataAnalystSynthesizerAgent
   */
  async generateResearchCharts(analysisContent: string, title: string): Promise<ChartGenerationResult[]> {
    try {
      console.log(`[${this.name}] Coordinating research visualization chart generation`);

      // Delegate chart generation to DataAnalystSynthesizerAgent
      const chartTask = {
        instruction: `Generate visualization charts for research findings: ${title}`,
        analysisContent,
        title,
        chartTypes: ['bar', 'line', 'pie'],
        requirements: [
          'Chart type selection based on data structure',
          'Clear titles and labels',
          'Data structure optimization',
          'Key insights identification'
        ]
      };

      const delegationResult = await this.delegateToAgent('data-analyst', 'generateCharts', chartTask);

      if (!this.validateDelegationResult(delegationResult, 'chart generation')) {
        console.warn(`[${this.name}] Chart generation delegation failed, using fallback`);
        return this.generateFallbackCharts(analysisContent, title);
      }

      console.log(`[${this.name}] Chart generation successfully delegated to DataAnalystSynthesizerAgent`);

      // In a real implementation, this would return the actual charts from the specialist agent
      // For now, return a structured response indicating successful delegation
      return [{
        success: true,
        chartConfig: {
          chartType: 'bar',
          title: title,
          data: [],
          explanation: `Chart generation delegated to DataAnalystSynthesizerAgent for: ${title}`
        },
        delegated: true,
        delegatedTo: 'data-analyst',
        delegatedAt: new Date()
      } as any];

    } catch (error: any) {
      console.error(`[${this.name}] Error coordinating chart generation:`, error);
      return this.generateFallbackCharts(analysisContent, title);
    }
  }

  /**
   * Fallback chart generation when delegation fails
   */
  private async generateFallbackCharts(analysisContent: string, title: string): Promise<ChartGenerationResult[]> {
    console.log(`[${this.name}] Using fallback chart generation`);

    return [{
      success: false,
      error: 'Chart generation delegation failed - requires DataAnalystSynthesizerAgent implementation',
      chartConfig: {
        chartType: 'bar',
        title: title,
        data: [],
        explanation: `Fallback response for: ${title}. Analysis content: ${analysisContent.substring(0, 100)}...`
      }
    } as any];
  }
}
