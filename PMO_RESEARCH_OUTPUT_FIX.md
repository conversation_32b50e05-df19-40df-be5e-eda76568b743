# PMO Research Output Duplication Fix

## Problem
Multiple research output items were being added to the PMO Output tab for each PMO Research task, instead of only showing the 'Research Strategy' output.

## Root Cause Analysis
The research workflow was creating **3 separate outputs** for each PMO Research task:

1. **Research Strategy** (from `ResearchLeadAgent.createPMOStrategicPlan()`)
2. **Research Analysis** (from `research-agent-collaboration` API route)  
3. **Research Team Output** (from `ResearchTeamAgent.storeAgentOutput()`)

All three were being stored in the global `Agent_Output` collection with `agentType: 'Research'`, causing them all to appear in the PMO Output tab.

## Solution Implemented

### 1. **Modified research-agent-collaboration API route**
**File:** `app/api/research-agent-collaboration/route.ts`
**Change:** Disabled storage for PMO tasks to prevent duplicate outputs
```typescript
// Skip storage for PMO tasks as ResearchTeamAgent already handles this to prevent duplicates
if (result.success && result.output && false) { // Temporarily disable to prevent duplicate PMO outputs
```

### 2. **Modified ResearchTeamAgent**
**File:** `lib/agents/research/ResearchTeamAgent.ts`
**Change:** Skip storage for strategic tasks since ResearchLeadAgent handles them
```typescript
// Only store if this is not a strategic task (strategic tasks are handled by ResearchLeadAgent)
const isPMOStrategicTask = this.isPMOStrategicTask(task);
if (!isPMOStrategicTask) {
  await this.storeAgentOutput(task, output, result);
} else {
  console.log(`[ResearchTeamAgent] Skipping duplicate storage for strategic task - handled by ResearchLeadAgent`);
}
```

### 3. **Enhanced ResearchLeadAgent strategic plan storage**
**File:** `lib/agents/research/ResearchLeadAgent.ts`
**Change:** Improved metadata and title for proper PMO Output tab display
```typescript
agentType: 'Research', // Research team agent type for PMO filtering
title: 'Research Strategy', // Clear title for PMO Output tab
// Include metadata for PMO Output tab
metadata: {
  recordTitle: 'Research Strategy',
  projectTitle: params.projectTitle,
  source: 'PMO',
  pmoId: params.pmoId,
  teamName: 'Research',
  teamId: 'Research',
  outputType: 'strategic_plan'
},
```

## Expected Result
Now only **one output** should appear in the PMO Output tab for each PMO Research task:
- **Research Strategy** - The strategic implementation plan created by ResearchLeadAgent

## Testing
To test the fix:
1. Create a new PMO Assessment
2. Click "Send to Research" 
3. Wait for research task completion
4. Check PMO Output tab - should only show "Research Strategy" output

## Files Modified
1. `app/api/research-agent-collaboration/route.ts` - Disabled duplicate storage for PMO tasks
2. `lib/agents/research/ResearchTeamAgent.ts` - Skip storage for strategic tasks
3. `lib/agents/research/ResearchLeadAgent.ts` - Enhanced strategic plan metadata

## Impact
- ✅ Eliminates duplicate research outputs in PMO Output tab
- ✅ Maintains proper research workflow functionality
- ✅ Preserves all research data (just prevents duplicate display)
- ✅ Clear "Research Strategy" title for easy identification
