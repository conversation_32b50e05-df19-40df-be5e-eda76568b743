// research/ResearchAgentManager.ts

import { ResearchAgent } from './ResearchAgent';
import { ResearchLeadAgent } from './ResearchLeadAgent';
import { InformationRetrievalAgent } from './InformationRetrievalAgent';
import { DataAnalystSynthesizerAgent } from './DataAnalystSynthesizerAgent';
import { ReportWriterFormatterAgent } from './ReportWriterFormatterAgent';
import { QualityAssuranceReviewerAgent } from './QualityAssuranceReviewerAgent';
import { AgentMemoryManager } from '../AgentMemoryManager';
import { LlmProvider } from '../../tools/llm-tool';
import { ResearchPlan, ResearchTaskRequest } from './ResearchInterfaces'; // Adjust path
import { AgenticTeamId } from '../pmo/PMOInterfaces'; // PMO Integration

export interface ResearchAgentTeam {
  researchLead: ResearchLeadAgent;
  infoRetriever: InformationRetrievalAgent; // Can have multiple later
  analystSynthesizer: DataAnalystSynthesizerAgent;
  reportWriter: ReportWriterFormatterAgent;
  qaReviewer: QualityAssuranceReviewerAgent;
}

export interface ResearchAgentManagerConfig {
  userId: string;
  defaultLlmProvider?: LlmProvider;
  defaultLlmModel?: string;
}

/**
 * Manages and coordinates the Research Agent team
 */
export class ResearchAgentManager {
  private agents: Map<string, ResearchAgent> = new Map();
  private memoryManager: AgentMemoryManager;
  private userId: string;
  private defaultLlmProvider: LlmProvider;
  private defaultLlmModel: string;
  private team?: ResearchAgentTeam; // Hold initialized team instance

  constructor(config: ResearchAgentManagerConfig) {
    this.userId = config.userId;
    this.defaultLlmProvider = config.defaultLlmProvider || 'openai';
    this.defaultLlmModel = config.defaultLlmModel || 'gpt-4o';
    // Ensure Memory Manager uses a path suitable for research agents if needed
    this.memoryManager = new AgentMemoryManager(this.userId); // Potentially adjust Firestore paths inside manager if needed
  }

  /**
   * Initialize the research agent team
   */
  async initializeResearchTeam(): Promise<ResearchAgentTeam> {
    console.log(`Initializing Research Team for user ${this.userId}...`);
    const researchLead = new ResearchLeadAgent(
      'research-lead', 'Research Lead', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );
    const infoRetriever = new InformationRetrievalAgent(
      'info-retriever-1', 'Information Retriever', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    ); // Name includes index for potential multiple retrievers
    const analystSynthesizer = new DataAnalystSynthesizerAgent(
      'data-analyst', 'Data Analyst & Synthesizer', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );
    const reportWriter = new ReportWriterFormatterAgent(
      'report-writer', 'Report Writer & Formatter', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );
    const qaReviewer = new QualityAssuranceReviewerAgent(
      'qa-reviewer', 'Quality Assurance Reviewer', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );

    this.registerAgent(researchLead);
    this.registerAgent(infoRetriever);
    this.registerAgent(analystSynthesizer);
    this.registerAgent(reportWriter);
    this.registerAgent(qaReviewer);

    this.team = {
        researchLead,
        infoRetriever,
        analystSynthesizer,
        reportWriter,
        qaReviewer
    };
    console.log("Research Team Initialized.");
    return this.team;
  }

  /**
   * Register an agent with the manager
   */
  registerAgent(agent: ResearchAgent): void {
    const agentInfo = agent.getInfo();
    if (this.agents.has(agentInfo.id)) {
        console.warn(`Agent with ID ${agentInfo.id} (${agentInfo.name}) is already registered. Overwriting.`);
    }
    this.agents.set(agentInfo.id, agent);
    console.log(`Research Agent registered: ${agentInfo.name} (${agentInfo.id})`);
  }

  /**
   * Get an agent by ID
   */
  getAgent(agentId: string): ResearchAgent | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Get all registered agents
   */
  getAllAgents(): ResearchAgent[] {
    return Array.from(this.agents.values());
  }

   /**
    * Get the initialized team structure
    */
   getTeam(): ResearchAgentTeam | undefined {
       return this.team;
   }

  /**
   * Start a new research task. Orchestrates the initial call to the Research Lead.
   * @param request - The research task details.
   * @returns The main task ID (or plan ID) initiated by the lead.
   */
  async startResearchTask(request: ResearchTaskRequest): Promise<string> {
    if (!this.team) {
      console.log("Team not initialized. Initializing now...");
      await this.initializeResearchTeam();
    }
    if (!this.team) { // Check again after initialization attempt
        throw new Error("Research team could not be initialized.");
    }

    const leadAgent = this.team.researchLead;
    if (!leadAgent) {
      throw new Error("Research Lead agent not found in the initialized team.");
    }

    console.log(`[Manager] Starting research task: ${request.topic} via agent ${leadAgent.getInfo().id}`);
    // Assign a unique ID if not provided
    if (!request.taskId) {
        request.taskId = `task-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    }

    try {
      // The lead agent handles the decomposition and assignment internally
      const planId = await leadAgent.initiateResearchTask(request);
      console.log(`[Manager] Research task initiated. Plan ID: ${planId}`);

      // The manager *could* monitor the overall plan status here,
      // or handle message routing if agents don't know each other's exact IDs.
      // For now, we assume the Lead manages the internal workflow.
      await this.routeTasksFromLead(planId); // Example: Resolve agent IDs

      return planId; // Return the identifier for the research process
    } catch (error) {
      console.error(`[Manager] Failed to start research task:`, error);
      throw error;
    }
  }


   /**
    * Resolve placeholder agent roles in a plan to actual agent IDs and assign.
    * This is a crucial step for the manager.
    */
   async routeTasksFromLead(planId: string): Promise<void> {
       const leadAgent = this.team?.researchLead;
       if (!leadAgent) return;

       // Retrieve the plan (assuming it's stored in lead's memory)
       const plan: ResearchPlan | undefined = leadAgent['memory'].Agent_Response[`plan_${planId}`]; // Accessing protected/private for example
       if (!plan) {
           console.error(`[Manager] Plan ${planId} not found in Lead's memory.`);
           return;
       }

       const agentRoleMap: { [key: string]: string | undefined } = {
           'InformationRetrieval': this.team?.infoRetriever.getInfo().id,
           'DataAnalystSynthesizer': this.team?.analystSynthesizer.getInfo().id,
           'ReportWriterFormatter': this.team?.reportWriter.getInfo().id,
           'QualityAssuranceReviewer': this.team?.qaReviewer.getInfo().id,
           'ResearchLead': this.team?.researchLead.getInfo().id, // Lead might assign tasks back?
       };

       for (const subTask of plan.subTasks) {
           if (subTask.assignedToAgentId.startsWith('agent_for_')) {
               const role = subTask.assignedToAgentId.replace('agent_for_', '');
               const actualAgentId = agentRoleMap[role];

               if (actualAgentId) {
                   subTask.assignedToAgentId = actualAgentId; // Resolve the ID in the plan

                   // Check dependencies before assigning (improved check)
                   const inputsReady = (subTask.requiredInputs || []).every(inputId => {
                       const inputTask = plan.subTasks.find(st => st.subTaskId === inputId);
                       return inputTask?.status === 'completed';
                   });

                   if (subTask.status === 'pending' && inputsReady) {
                       console.log(`[Manager] Routing task ${subTask.subTaskId} to actual agent ${actualAgentId} (${role})`);

                       // Create the task persistently via Manager/MemoryManager or directly via Lead
                       // Using lead to create the task for consistency:
                       await leadAgent.createTask(
                           `Sub-Task: ${subTask.instruction.substring(0, 50)}...`, // Title
                           subTask.instruction, // Description
                           'medium', // Priority
                           actualAgentId, // Assigned Agent
                           undefined, // Due date
                           { // Metadata
                               planId: plan.planId,
                               subTaskId: subTask.subTaskId,
                               parentTaskId: plan.parentTaskId,
                               // Include necessary input data here by fetching results of completed dependencies
                           }
                       );
                        // Update local status in the plan held by the lead
                        subTask.status = 'in-progress'; // Or keep pending until agent picks up?
                   }
               } else {
                   console.error(`[Manager] Could not find agent for role: ${role} for task ${subTask.subTaskId}`);
                    subTask.status = 'failed'; // Cannot assign
               }
           }
       }
        // Save the updated plan with resolved IDs back to the lead's memory
        leadAgent['memory'].Agent_Response[`plan_${planId}`] = plan;
        await leadAgent['saveMemoryToStorage'](); // Trigger save
        console.log(`[Manager] Task routing for plan ${planId} complete.`);
   }


  /**
   * Get the memory manager instance
   */
  getMemoryManager(): AgentMemoryManager {
    return this.memoryManager;
  }

  /**
   * PMO Integration: Start a PMO research task
   * This method integrates with PMO workflows while maintaining research expertise
   */
  async startPMOResearchTask(params: {
    pmoId: string;
    projectTitle: string;
    projectDescription: string;
    pmoAssessment: string;
    teamSelectionRationale: string;
    priority: string;
    category: string;
    requirementsDocument?: string;
  }): Promise<{
    success: boolean;
    strategicPlan?: string;
    documentTitle?: string;
    documentUrl?: string;
    researchPlanId?: string;
    error?: string;
  }> {
    try {
      if (!this.team) {
        console.log("Team not initialized. Initializing now...");
        await this.initializeResearchTeam();
      }
      if (!this.team) {
        throw new Error("Research team could not be initialized.");
      }

      const leadAgent = this.team.researchLead;
      console.log(`[Manager] Starting PMO research task: ${params.projectTitle} via agent ${leadAgent.getInfo().id}`);

      // Create PMO strategic plan using the enhanced ResearchLeadAgent
      const strategicPlanResult = await leadAgent.createPMOStrategicPlan(params);

      if (!strategicPlanResult.success) {
        throw new Error(strategicPlanResult.error || 'Failed to create PMO strategic plan');
      }

      // Convert PMO project to research task request
      const researchRequest: ResearchTaskRequest = {
        taskId: `${params.pmoId}-${Date.now()}`,
        topic: params.projectTitle,
        scope: params.projectDescription,
        requiredDepth: 'deep',
        outputFormat: 'report',
        deadline: undefined,
        requesterInfo: `PMO Project ${params.pmoId}`
      };

      // Start traditional research workflow in parallel
      const researchPlanId = await leadAgent.initiateResearchTask(researchRequest);

      // Route tasks to appropriate teams (both research and cross-functional)
      await this.routeTasksFromLead(researchPlanId);

      console.log(`[Manager] PMO research task initiated. Strategic Plan: ${strategicPlanResult.documentTitle}, Research Plan: ${researchPlanId}`);

      return {
        success: true,
        strategicPlan: strategicPlanResult.strategicPlan,
        documentTitle: strategicPlanResult.documentTitle,
        documentUrl: strategicPlanResult.documentUrl,
        researchPlanId,
        error: strategicPlanResult.error
      };
    } catch (error: any) {
      console.error(`[Manager] Failed to start PMO research task:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * PMO Integration: Analyze task for team recommendation
   * Delegates to the enhanced ResearchLeadAgent
   */
  async analyzePMOTask(params: {
    title: string;
    description: string;
    context: string;
    taskType: string;
  }): Promise<{
    success: boolean;
    recommendedTeams?: AgenticTeamId[];
    rationale?: string;
    error?: string;
  }> {
    try {
      if (!this.team) {
        await this.initializeResearchTeam();
      }
      if (!this.team) {
        throw new Error("Research team could not be initialized.");
      }

      const leadAgent = this.team.researchLead;
      return await leadAgent.analyzeTask(params);
    } catch (error: any) {
      console.error(`[Manager] Failed to analyze PMO task:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * PMO Integration: Retrieve PMO tasks and requirements
   * Delegates to the enhanced ResearchLeadAgent
   */
  async retrievePMOTasks(
    pmoId: string,
    userRequest: string,
    modelNameOrProvider?: string
  ): Promise<{
    success: boolean;
    tasks: string[];
    assessment: string;
    requirements: string;
    error?: string;
  }> {
    try {
      if (!this.team) {
        await this.initializeResearchTeam();
      }
      if (!this.team) {
        throw new Error("Research team could not be initialized.");
      }

      const leadAgent = this.team.researchLead;
      return await leadAgent.retrievePMOTasks(pmoId, userRequest, modelNameOrProvider);
    } catch (error: any) {
      console.error(`[Manager] Failed to retrieve PMO tasks:`, error);
      return {
        success: false,
        tasks: [],
        assessment: '',
        requirements: '',
        error: error.message
      };
    }
  }

  /**
   * Enhanced task routing that supports cross-team coordination
   * Extends the original method to handle PMO team assignments
   */
  async routeTasksFromLeadEnhanced(planId: string, crossTeamEnabled: boolean = false): Promise<void> {
    const leadAgent = this.team?.researchLead;
    if (!leadAgent) return;

    // First, handle traditional research team routing
    await this.routeTasksFromLead(planId);

    // If cross-team coordination is enabled, handle strategic task collections
    if (crossTeamEnabled) {
      try {
        // Check if there are strategic task collections that need cross-team routing
        const strategicCollections = leadAgent['activeStrategicCollections'] || new Map();

        for (const [collectionId, collection] of strategicCollections) {
          console.log(`[Manager] Processing strategic task collection: ${collectionId}`);

          // Log team assignments for cross-functional coordination
          Object.entries(collection.teamAssignments).forEach(([teamName, assignment]) => {
            console.log(`[Manager] Team ${teamName}: ${assignment.taskCount} tasks, ${assignment.estimatedWorkload}`);
          });

          // Here you would integrate with other team managers
          // For now, we log the cross-team coordination requirements
          const crossTeamTasks = collection.tasks.filter(task =>
            !task.assignedTeam.includes('Research') &&
            task.assignedTeam !== 'Strategic Director'
          );

          if (crossTeamTasks.length > 0) {
            console.log(`[Manager] ${crossTeamTasks.length} tasks require cross-team coordination`);
            // TODO: Integrate with other team managers (Marketing, Sales, etc.)
          }
        }
      } catch (error) {
        console.error(`[Manager] Error in enhanced task routing:`, error);
      }
    }
  }

  /**
   * Get enhanced team capabilities including PMO integration
   */
  getEnhancedTeamCapabilities(): {
    researchCapabilities: string[];
    pmoIntegration: string[];
    crossTeamCoordination: string[];
    strategicPlanning: string[];
  } {
    return {
      researchCapabilities: [
        'Information retrieval and analysis',
        'Data synthesis and insights generation',
        'Report writing and formatting',
        'Quality assurance and review',
        'Academic and web research'
      ],
      pmoIntegration: [
        'PMO task analysis and team recommendation',
        'PMO document retrieval and processing',
        'Strategic implementation plan creation',
        'PMO compliance and standards adherence',
        'Cross-functional project coordination'
      ],
      crossTeamCoordination: [
        'Marketing team collaboration',
        'Sales team research support',
        'Business analysis integration',
        'Software design research requirements',
        'Content team strategic guidance'
      ],
      strategicPlanning: [
        'Strategic task collection creation',
        'Timeline and milestone planning',
        'Resource allocation optimization',
        'Risk assessment and mitigation',
        'Success metrics definition'
      ]
    };
  }
}